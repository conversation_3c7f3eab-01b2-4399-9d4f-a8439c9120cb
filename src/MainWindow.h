/**
 * @file MainWindow.h
 * @brief Main application window for ARINC 424 Viewer
 *
 * This file contains the MainWindow class which serves as the primary interface
 * for the ARINC 424 aviation data visualization application.
 */

#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QStatusBar>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QFileDialog>
#include <QProgressBar>
#include <QLabel>
#include <QTimer>
#include <memory>

#include "DataModels.h"
#include "PythonInterface.h"
#include "MapWidget.h"
#include "FilterWidget.h"

/**
 * @class MainWindow
 * @brief Main application window for ARINC 424 data visualization
 *
 * The MainWindow class provides the primary user interface for loading,
 * filtering, and visualizing ARINC 424 aviation navigation data.
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget (optional)
     */
    MainWindow(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~MainWindow();

private slots:
    /**
     * @brief Opens a file dialog to select and load an ARINC 424 file
     */
    void openFile();

    /**
     * @brief Handles filter changes and updates the display accordingly
     */
    void onFiltersChanged();

    /**
     * @brief Updates the map display with filtered data
     */
    void updateMapDisplay();

    /**
     * @brief Shows the about dialog
     */
    void showAbout();

private:
    /**
     * @brief Sets up the main user interface components
     */
    void setupUI();

    /**
     * @brief Configures the application menu bar
     */
    void setupMenuBar();

    /**
     * @brief Initializes the status bar components
     */
    void setupStatusBar();

    /**
     * @brief Connects signals and slots for UI components
     */
    void connectSignals();

    /**
     * @brief Initializes filter options after data loading
     */
    void initializeFilterOptions();

    /**
     * @brief Updates filter options based on current selections
     */
    void updateFilterOptions();

    /**
     * @brief Updates the status bar with current information
     */
    void updateStatusBar();

    /**
     * @brief Validates the selected file before loading
     * @param fileName Path to the file to validate
     * @return true if file is valid, false otherwise
     */
    bool validateFile(const QString& fileName);

    /**
     * @brief Sets up UI state for file loading operation
     */
    void prepareForFileLoading();

    /**
     * @brief Restores UI state after file loading operation
     */
    void restoreAfterFileLoading();

    /**
     * @brief Handles successful file loading
     */
    void handleFileLoadSuccess();

    /**
     * @brief Handles file loading failure
     * @param errorMessage Error message to display
     */
    void handleFileLoadError(const QString& errorMessage);
    
    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    QWidget* m_leftPanel;
    QVBoxLayout* m_leftLayout;
    
    QPushButton* m_openFileBtn;
    FilterWidget* m_filterWidget;
    MapWidget* m_mapWidget;
    
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QLabel* m_recordCountLabel;
    
    // Data and logic
    std::unique_ptr<ArincDataModel> m_dataModel;
    std::unique_ptr<PythonInterface> m_pythonInterface;

    QString m_currentFilePath;
    QTimer* m_updateTimer;
};
